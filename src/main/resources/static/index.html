<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spring AI 演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .chat-container {
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .chat-header {
            background-color: #4CAF50;
            color: white;
            padding: 10px;
            font-weight: bold;
        }
        .chat-messages {
            height: 300px;
            overflow-y: auto;
            padding: 10px;
            background-color: #f9f9f9;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 18px;
            max-width: 70%;
            word-wrap: break-word;
        }
        .user-message {
            background-color: #DCF8C6;
            margin-left: auto;
            border-bottom-right-radius: 4px;
        }
        .ai-message {
            background-color: #ECECEC;
            margin-right: auto;
            border-bottom-left-radius: 4px;
        }
        .chat-input {
            display: flex;
            padding: 10px;
            background-color: #f0f0f0;
        }
        .chat-input input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            outline: none;
        }
        .chat-input button {
            margin-left: 10px;
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .chat-input button:hover {
            background-color: #45a049;
        }
        .template-section {
            margin-top: 30px;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Spring AI 演示</h1>
        
        <div class="chat-container">
            <div class="chat-header">AI 聊天</div>
            <div class="chat-messages" id="chatMessages"></div>
            <div class="chat-input">
                <input type="text" id="messageInput" placeholder="输入您的问题..." />
                <button id="sendButton">发送</button>
            </div>
        </div>
        
        <div class="template-section">
            <h2>使用模板</h2>
            <p>输入一个主题，AI将为您提供简短介绍：</p>
            <div class="chat-input">
                <input type="text" id="topicInput" placeholder="输入主题..." />
                <button id="sendTopicButton">获取介绍</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chatMessages = document.getElementById('chatMessages');
            const messageInput = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');
            const topicInput = document.getElementById('topicInput');
            const sendTopicButton = document.getElementById('sendTopicButton');
            
            // 发送普通消息
            function sendMessage() {
                const message = messageInput.value.trim();
                if (message) {
                    // 添加用户消息
                    addMessage(message, 'user');
                    messageInput.value = '';
                    
                    // 显示加载中
                    const loadingId = showLoading();
                    
                    // 发送请求到后端
                    fetch(`/api/ai/chat?message=${encodeURIComponent(message)}`)
                        .then(response => response.text())
                        .then(data => {
                            // 移除加载中
                            removeLoading(loadingId);
                            // 添加AI回复
                            addMessage(data, 'ai');
                        })
                        .catch(error => {
                            // 移除加载中
                            removeLoading(loadingId);
                            // 显示错误
                            addMessage('抱歉，发生了错误: ' + error.message, 'ai');
                        });
                }
            }
            
            // 使用模板发送消息
            function sendTopicMessage() {
                const topic = topicInput.value.trim();
                if (topic) {
                    // 添加用户消息
                    addMessage(`请介绍: ${topic}`, 'user');
                    topicInput.value = '';
                    
                    // 显示加载中
                    const loadingId = showLoading();
                    
                    // 发送请求到后端
                    fetch(`/api/ai/chat-with-template?topic=${encodeURIComponent(topic)}`)
                        .then(response => response.text())
                        .then(data => {
                            // 移除加载中
                            removeLoading(loadingId);
                            // 添加AI回复
                            addMessage(data, 'ai');
                        })
                        .catch(error => {
                            // 移除加载中
                            removeLoading(loadingId);
                            // 显示错误
                            addMessage('抱歉，发生了错误: ' + error.message, 'ai');
                        });
                }
            }
            
            // 添加消息到聊天窗口
            function addMessage(text, sender) {
                const messageElement = document.createElement('div');
                messageElement.classList.add('message');
                messageElement.classList.add(sender === 'user' ? 'user-message' : 'ai-message');
                messageElement.textContent = text;
                chatMessages.appendChild(messageElement);
                
                // 滚动到底部
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
            
            // 显示加载中
            function showLoading() {
                const loadingElement = document.createElement('div');
                loadingElement.classList.add('loading');
                loadingElement.textContent = '正在思考...';
                chatMessages.appendChild(loadingElement);
                chatMessages.scrollTop = chatMessages.scrollHeight;
                return Date.now(); // 返回一个唯一ID
            }
            
            // 移除加载中
            function removeLoading(id) {
                const loadingElements = document.getElementsByClassName('loading');
                if (loadingElements.length > 0) {
                    chatMessages.removeChild(loadingElements[0]);
                }
            }
            
            // 事件监听
            sendButton.addEventListener('click', sendMessage);
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
            
            sendTopicButton.addEventListener('click', sendTopicMessage);
            topicInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendTopicMessage();
                }
            });
            
            // 添加欢迎消息
            addMessage('你好！我是Spring AI助手，有什么可以帮助你的吗？', 'ai');
        });
    </script>
</body>
</html>