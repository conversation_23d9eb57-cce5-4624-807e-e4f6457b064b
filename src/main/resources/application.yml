# 服务器配置
server:
  port: 8081

# Spring AI配置
spring:
  ai:
    # OpenAI配置（已禁用）
    openai:
      enabled: false
      api-key: ${OPENAI_API_KEY:disabled}
      chat:
        options:
          model: gpt-3.5-turbo
          temperature: 0.7
    # Kimi API配置
    kimi:
      api-key: sk-WX2OrkHvl74wE7AbZcKiZYt6HHXv22i5CXfqxm7v9juFuMvY
      model: moonshotai/kimi-k2:free
      temperature: 0.7
      site-url: https://example.com
      site-name: Spring AI Demo

# 日志配置
logging:
  level:
    root: INFO
    org.springframework.ai: DEBUG
    com.example.springaidemo: DEBUG
