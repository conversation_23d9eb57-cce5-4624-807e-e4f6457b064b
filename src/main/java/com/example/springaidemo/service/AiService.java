package com.example.springaidemo.service;

import com.example.springaidemo.model.ChatClient;
import com.example.springaidemo.model.ChatMessage;
import com.example.springaidemo.model.ChatResponse;
import com.example.springaidemo.model.PromptTemplate;
import com.example.springaidemo.model.UserChatMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Map;

/**
 * AI服务类
 * 封装与AI模型交互的业务逻辑
 */
@Service
public class AiService {

    private final ChatClient chatClient;

    /**
     * 构造函数，注入ChatClient
     * 
     * @param chatClient Spring AI提供的聊天客户端
     */
    @Autowired
    public AiService(ChatClient chatClient) {
        this.chatClient = chatClient;
    }

    /**
     * 发送消息给AI并获取回复
     * 
     * @param message 用户消息
     * @return AI回复内容
     */
    public String generateResponse(String message) {
        ChatResponse response = chatClient.sendMessages(Collections.singletonList(new UserChatMessage(message)));
        return response.getContent();
    }

    /**
     * 使用模板生成AI回复
     * 
     * @param templateText 模板文本
     * @param variables 模板变量
     * @return AI回复内容
     */
    public String generateResponseWithTemplate(String templateText, Map<String, Object> variables) {
        PromptTemplate promptTemplate = new PromptTemplate(templateText);
        ChatMessage message = promptTemplate.createMessage(variables);
        ChatResponse response = chatClient.sendMessages(Collections.singletonList(message));
        return response.getContent();
    }
}