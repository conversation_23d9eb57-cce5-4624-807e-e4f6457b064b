package com.example.springaidemo.service;

import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * AI服务类
 * 封装与AI模型交互的业务逻辑
 */
@Service
public class AiService {

    private final ChatModel chatModel;

    /**
     * 构造函数，注入ChatModel
     *
     * @param chatModel Spring AI提供的聊天模型
     */
    @Autowired
    public AiService(ChatModel chatModel) {
        this.chatModel = chatModel;
    }

    /**
     * 发送消息给AI并获取回复
     *
     * @param message 用户消息
     * @return AI回复内容
     */
    public String generateResponse(String message) {
        ChatResponse response = chatModel.call(new Prompt(message));
        return response.getResult().getOutput().getContent();
    }

    /**
     * 使用模板生成AI回复
     *
     * @param templateText 模板文本
     * @param variables 模板变量
     * @return AI回复内容
     */
    public String generateResponseWithTemplate(String templateText, Map<String, Object> variables) {
        PromptTemplate promptTemplate = new PromptTemplate(templateText);
        Prompt prompt = promptTemplate.create(variables);
        ChatResponse response = chatModel.call(prompt);
        return response.getResult().getOutput().getContent();
    }
}