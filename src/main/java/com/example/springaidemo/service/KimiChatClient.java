package com.example.springaidemo.service;

import com.example.springaidemo.config.KimiAiProperties;
import com.example.springaidemo.model.ChatClient;
import com.example.springaidemo.model.ChatMessage;
import com.example.springaidemo.model.ChatResponse;
import com.example.springaidemo.model.kimi.KimiChatRequest;
import com.example.springaidemo.model.kimi.KimiChatResponse;
import com.example.springaidemo.model.kimi.KimiMessage;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestClient;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Kimi聊天客户端实现
 */
public class KimiChatClient implements ChatClient {

    private final RestClient restClient;
    private final KimiAiProperties properties;

    /**
     * 构造函数
     * 
     * @param properties Kimi API配置属性
     */
    public KimiChatClient(KimiAiProperties properties) {
        this.properties = properties;
        this.restClient = RestClient.builder()
                .baseUrl(properties.getBaseUrl())
                .build();
    }

    @Override
    public ChatResponse sendMessages(List<ChatMessage> messages) {
        List<KimiMessage> kimiMessages = convertToKimiMessages(messages);
        KimiChatRequest request = new KimiChatRequest(
                properties.getModel(),
                kimiMessages,
                properties.getTemperature()
        );

        KimiChatResponse response = restClient.post()
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", "Bearer " + properties.getApiKey())
                .header("HTTP-Referer", properties.getSiteUrl())
                .header("X-Title", properties.getSiteName())
                .body(request)
                .retrieve()
                .body(KimiChatResponse.class);

        return convertToChatResponse(response);
    }

    /**
     * 将聊天消息转换为Kimi消息
     * 
     * @param messages 聊天消息列表
     * @return Kimi消息列表
     */
    private List<KimiMessage> convertToKimiMessages(List<ChatMessage> messages) {
        return messages.stream()
                .map(message -> new KimiMessage(message.getRole(), message.getContent()))
                .collect(Collectors.toList());
    }

    /**
     * 将Kimi响应转换为聊天响应
     * 
     * @param kimiResponse Kimi API响应
     * @return 聊天响应
     */
    private ChatResponse convertToChatResponse(KimiChatResponse kimiResponse) {
        if (kimiResponse == null || kimiResponse.getChoices() == null || kimiResponse.getChoices().isEmpty()) {
            throw new RuntimeException("无效的Kimi API响应");
        }

        KimiChatResponse.KimiChoice choice = kimiResponse.getChoices().get(0);
        KimiMessage kimiMessage = choice.getMessage();
        return new ChatResponse(kimiMessage.getContent(), choice.getFinishReason());
    }
}