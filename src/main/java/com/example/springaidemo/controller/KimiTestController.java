package com.example.springaidemo.controller;

import com.example.springaidemo.model.ChatClient;
import com.example.springaidemo.model.ChatResponse;
import com.example.springaidemo.model.UserChatMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;

/**
 * Kimi测试控制器
 * 用于测试Kimi API的集成
 */
@RestController
@RequestMapping("/api/kimi")
public class KimiTestController {

    private final ChatClient chatClient;

    @Autowired
    public KimiTestController(ChatClient chatClient) {
        this.chatClient = chatClient;
    }

    /**
     * 测试Kimi聊天接口
     * 
     * @param message 用户输入的消息
     * @return Kimi的回复
     */
    @GetMapping("/chat")
    public String chat(@RequestParam String message) {
        ChatResponse response = chatClient.sendMessages(Collections.singletonList(new UserChatMessage(message)));
        return response.getContent();
    }
}