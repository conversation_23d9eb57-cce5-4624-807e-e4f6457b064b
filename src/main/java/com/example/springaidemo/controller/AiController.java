package com.example.springaidemo.controller;

import org.springframework.ai.chat.ChatClient;
import org.springframework.ai.chat.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * AI控制器
 * 提供与AI模型交互的REST API
 */
@RestController
@RequestMapping("/api/ai")
public class AiController {

    private final ChatClient chatClient;

    /**
     * 构造函数，注入ChatClient
     *
     * @param chatClient Spring AI提供的聊天客户端
     */
    @Autowired
    public AiController(ChatClient chatClient) {
        this.chatClient = chatClient;
    }

    /**
     * 简单的聊天接口
     *
     * @param message 用户输入的消息
     * @return AI的回复
     */
    @GetMapping("/chat")
    public String chat(@RequestParam String message) {
        ChatResponse response = chatClient.call(new Prompt(message));
        return response.getResult().getOutput().getContent();
    }

    /**
     * 使用模板的聊天接口
     *
     * @param topic 聊天主题
     * @return 基于主题的AI回复
     */
    @GetMapping("/chat-with-template")
    public String chatWithTemplate(@RequestParam String topic) {
        String template = "请用简短的中文介绍{topic}，不超过100字";
        PromptTemplate promptTemplate = new PromptTemplate(template);
        Prompt prompt = promptTemplate.create(Map.of("topic", topic));
        ChatResponse response = chatClient.call(prompt);
        return response.getResult().getOutput().getContent();
    }
}