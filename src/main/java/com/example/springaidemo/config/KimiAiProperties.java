package com.example.springaidemo.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Kimi AI API配置属性
 */
@ConfigurationProperties(prefix = "spring.ai.kimi")
public class KimiAiProperties {

    /**
     * Kimi API密钥
     */
    private String apiKey;

    /**
     * Kimi API基础URL
     */
    private String baseUrl = "https://openrouter.ai/api/v1/chat/completions";

    /**
     * 模型名称
     */
    private String model = "moonshotai/kimi-k2:free";

    /**
     * 温度参数，控制输出的随机性
     */
    private float temperature = 0.7f;

    /**
     * 网站URL，用于OpenRouter的排名
     */
    private String siteUrl = "";

    /**
     * 网站名称，用于OpenRouter的排名
     */
    private String siteName = "";

    // Getters and Setters
    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public float getTemperature() {
        return temperature;
    }

    public void setTemperature(float temperature) {
        this.temperature = temperature;
    }

    public String getSiteUrl() {
        return siteUrl;
    }

    public void setSiteUrl(String siteUrl) {
        this.siteUrl = siteUrl;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }
}