package com.example.springaidemo.config;

import com.example.springaidemo.model.ChatClient;
import com.example.springaidemo.service.OpenAiChatClient;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * OpenAI配置类
 */
@Configuration
@ConditionalOnProperty(prefix = "spring.ai.openai", name = "api-key")
public class OpenAiConfig {

    @Value("${spring.ai.openai.api-key}")
    private String apiKey;

    /**
     * 创建OpenAI API客户端
     */
    @Bean
    public OpenAiApi openAiApi() {
        return new OpenAiApi(apiKey);
    }

    /**
     * 创建OpenAI聊天模型
     */
    @Bean
    public OpenAiChatModel openAiChatModel(OpenAiApi openAiApi) {
        return new OpenAiChatModel(openAiApi);
    }

    /**
     * 创建自定义ChatClient Bean
     * 使用@Primary注解使其优先于其他ChatClient实现
     */
    @Bean
    @Primary
    public ChatClient openAiChatClient(OpenAiChatModel openAiChatModel) {
        return new OpenAiChatClient(openAiChatModel);
    }
}