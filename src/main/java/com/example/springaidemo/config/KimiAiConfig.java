package com.example.springaidemo.config;

import com.example.springaidemo.model.ChatClient;
import com.example.springaidemo.service.KimiChatClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Kimi AI配置类
 */
@Configuration
@EnableConfigurationProperties(KimiAiProperties.class)
@ConditionalOnProperty(prefix = "spring.ai.kimi", name = "api-key")
public class KimiAiConfig {

    /**
     * 创建KimiChatClient Bean
     * 使用@Primary注解使其优先于其他ChatClient实现
     */
    @Bean
    @Primary
    public ChatClient kimiChatClient(KimiAiProperties properties) {
        return new KimiChatClient(properties);
    }
}