package com.example.springaidemo.model;

/**
 * 聊天响应类
 */
public class ChatResponse {
    private final String content;
    private final String finishReason;
    
    public ChatResponse(String content, String finishReason) {
        this.content = content;
        this.finishReason = finishReason;
    }
    
    /**
     * 获取响应内容
     * 
     * @return 响应内容
     */
    public String getContent() {
        return content;
    }
    
    /**
     * 获取完成原因
     * 
     * @return 完成原因
     */
    public String getFinishReason() {
        return finishReason;
    }
}