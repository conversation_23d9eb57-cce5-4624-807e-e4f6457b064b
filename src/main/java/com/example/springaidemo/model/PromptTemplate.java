package com.example.springaidemo.model;

import java.util.Map;

/**
 * 提示模板类
 * 用于创建带有变量的提示模板
 */
public class PromptTemplate {
    
    private final String template;
    
    /**
     * 构造函数
     * 
     * @param template 模板字符串，使用{变量名}表示变量
     */
    public PromptTemplate(String template) {
        this.template = template;
    }
    
    /**
     * 使用变量填充模板并创建用户消息
     * 
     * @param variables 变量映射
     * @return 填充后的用户消息
     */
    public ChatMessage createMessage(Map<String, Object> variables) {
        String content = template;
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            content = content.replace("{" + entry.getKey() + "}", entry.getValue().toString());
        }
        return new UserChatMessage(content);
    }
}