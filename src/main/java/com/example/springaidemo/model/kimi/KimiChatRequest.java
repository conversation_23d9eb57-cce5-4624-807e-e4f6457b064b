package com.example.springaidemo.model.kimi;

import java.util.List;

/**
 * Kimi API聊天请求模型
 */
public class KimiChatRequest {
    private String model;
    private List<KimiMessage> messages;
    private float temperature;

    public KimiChatRequest() {
    }

    public KimiChatRequest(String model, List<KimiMessage> messages, float temperature) {
        this.model = model;
        this.messages = messages;
        this.temperature = temperature;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public List<KimiMessage> getMessages() {
        return messages;
    }

    public void setMessages(List<KimiMessage> messages) {
        this.messages = messages;
    }

    public float getTemperature() {
        return temperature;
    }

    public void setTemperature(float temperature) {
        this.temperature = temperature;
    }
}