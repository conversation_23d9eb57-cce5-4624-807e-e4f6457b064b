package com.example.springaidemo.model.kimi;

/**
 * Kimi API消息模型
 */
public class KimiMessage {
    private String role;
    private String content;

    public KimiMessage() {
    }

    public KimiMessage(String role, String content) {
        this.role = role;
        this.content = content;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}