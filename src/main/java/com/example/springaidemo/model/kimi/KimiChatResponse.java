package com.example.springaidemo.model.kimi;

import java.util.List;

/**
 * Kimi API聊天响应模型
 */
public class KimiChatResponse {
    private String id;
    private String object;
    private long created;
    private String model;
    private List<KimiChoice> choices;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getObject() {
        return object;
    }

    public void setObject(String object) {
        this.object = object;
    }

    public long getCreated() {
        return created;
    }

    public void setCreated(long created) {
        this.created = created;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public List<KimiChoice> getChoices() {
        return choices;
    }

    public void setChoices(List<KimiChoice> choices) {
        this.choices = choices;
    }

    /**
     * Kimi API响应选择模型
     */
    public static class KimiChoice {
        private KimiMessage message;
        private String finishReason;
        private int index;

        public KimiMessage getMessage() {
            return message;
        }

        public void setMessage(KimiMessage message) {
            this.message = message;
        }

        public String getFinishReason() {
            return finishReason;
        }

        public void setFinishReason(String finishReason) {
            this.finishReason = finishReason;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }
}