# Spring AI 演示项目

这是一个使用Spring AI框架的示例项目，展示了如何在Spring Boot应用程序中集成和使用AI功能。本项目使用Spring AI的OpenAI兼容接口来访问Moonshot AI (Kimi)。

## 项目结构

```
src
├── main
│   ├── java
│   │   └── com
│   │       └── example
│   │           └── springaidemo
│   │               ├── controller
│   │               │   └── AiController.java
│   │               ├── service
│   │               │   └── AiService.java
│   │               └── SpringAiDemoApplication.java
│   └── resources
│       └── application.properties
└── test
    └── java
        └── com
            └── example
                └── springaidemo
                    └── SpringAiDemoApplicationTests.java
```

## 功能特点

- 集成Spring AI框架
- 使用Spring AI原生OpenAI ChatClient
- 通过OpenAI兼容接口访问Moonshot AI (Kimi)
- 提供REST API与AI模型交互
- 支持简单的聊天功能
- 支持使用模板的聊天功能
- 现代化的Web界面

## 使用方法

### 前提条件

- Java 17或更高版本
- Maven 3.6或更高版本
- Moonshot AI API密钥

### 配置

本项目使用Moonshot AI (Kimi)，通过Spring AI的OpenAI兼容接口访问。

#### 获取API密钥

1. 访问 [Moonshot AI 开放平台](https://platform.moonshot.cn/)
2. 注册账号并获取API密钥

#### 配置方法

在`application.yml`中配置：

```yaml
spring:
  ai:
    openai:
      base-url: https://api.moonshot.cn/v1
      api-key: your_moonshot_api_key
      chat:
        options:
          model: kimi-k2-0711-preview
          temperature: 0.7
```

#### 环境变量设置（推荐）

```bash
# Windows
set MOONSHOT_API_KEY=your_api_key_here

# Linux/macOS
export MOONSHOT_API_KEY=your_api_key_here
```

然后在配置文件中使用环境变量：

```yaml
spring:
  ai:
    openai:
      base-url: https://api.moonshot.cn/v1
      api-key: ${MOONSHOT_API_KEY}
      chat:
        options:
          model: kimi-k2-0711-preview
          temperature: 0.7
```

### 构建和运行

```bash
# 构建项目
mvn clean package

# 运行应用程序
java -jar target/spring-ai-demo-0.0.1-SNAPSHOT.jar
```

### 访问应用

启动应用后，可以通过以下方式访问：

1. **Web界面**：访问 http://localhost:8081
2. **API接口**：
   - 简单聊天：`GET http://localhost:8081/api/ai/chat?message=你好`
   - 模板聊天：`GET http://localhost:8081/api/ai/chat-with-template?topic=人工智能`

## 项目结构

```
src/
├── main/
│   ├── java/com/example/springaidemo/
│   │   ├── SpringAiDemoApplication.java    # 主启动类
│   │   ├── controller/
│   │   │   └── AiController.java           # AI接口控制器
│   │   └── service/
│   │       └── AiService.java              # AI服务类
│   └── resources/
│       ├── static/
│       │   └── index.html                  # Web界面
│       └── application.yml                 # 配置文件
└── test/
    └── java/com/example/springaidemo/
        └── SpringAiDemoApplicationTests.java
```

## 技术特点

- **简化架构**：直接使用Spring AI原生ChatClient，无需自定义实现
- **OpenAI兼容**：通过OpenAI兼容接口访问Moonshot AI
- **自动配置**：Spring Boot自动配置，最小化配置代码
- **现代界面**：响应式Web界面，支持实时聊天

## 参考资料

- [Spring AI 官方文档](https://docs.spring.io/spring-ai/reference/index.html)
- [Spring Boot 官方文档](https://docs.spring.io/spring-boot/docs/current/reference/html/)
- [Moonshot AI 开放平台](https://platform.moonshot.cn/)