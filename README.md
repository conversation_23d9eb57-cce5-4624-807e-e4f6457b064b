# Spring AI 演示项目

这是一个使用Spring AI框架的示例项目，展示了如何在Spring Boot应用程序中集成和使用AI功能。本项目支持OpenAI和Kimi API，可以根据配置灵活切换。

## 项目结构

```
src
├── main
│   ├── java
│   │   └── com
│   │       └── example
│   │           └── springaidemo
│   │               ├── controller
│   │               │   └── AiController.java
│   │               ├── service
│   │               │   └── AiService.java
│   │               └── SpringAiDemoApplication.java
│   └── resources
│       └── application.properties
└── test
    └── java
        └── com
            └── example
                └── springaidemo
                    └── SpringAiDemoApplicationTests.java
```

## 功能特点

- 集成Spring AI框架
- 提供REST API与AI模型交互
- 支持OpenAI和Kimi API
- 支持简单的聊天功能
- 支持使用模板的聊天功能
- 自定义AI模型实现

## 使用方法

### 前提条件

- Java 17或更高版本
- Maven 3.6或更高版本
- OpenAI API密钥或Kimi API密钥

### 配置

在运行应用程序之前，需要设置API密钥。可以通过以下方式之一进行设置：

#### 使用OpenAI API

1. 设置环境变量：

```bash
export OPENAI_API_KEY=your_openai_api_key
```

2. 在`application.yml`中配置：

```yaml
spring:
  ai:
    openai:
      enabled: true
      api-key: your_openai_api_key
    kimi:
      enabled: false
```

#### 使用Kimi API

1. 设置环境变量：

```bash
export KIMI_API_KEY=your_kimi_api_key
```

2. 在`application.yml`中配置：

```yaml
spring:
  ai:
    openai:
      enabled: false
    kimi:
      api-key: your_kimi_api_key
      model: moonshotai/kimi-k2:free
```

注意：Kimi API通过OpenRouter平台访问，您需要在OpenRouter上注册并获取API密钥。

#### 环境变量设置方法

```bash
# Windows
set OPENAI_API_KEY=your_api_key_here
# 或
set KIMI_API_KEY=your_kimi_api_key_here

# Linux/macOS
export OPENAI_API_KEY=your_api_key_here
```

2. 或者直接在`application.properties`文件中设置（不推荐用于生产环境）：

```properties
spring.ai.openai.api-key=your_api_key_here
```

### 构建和运行

```bash
# 构建项目
mvn clean package

# 运行应用程序
java -jar target/spring-ai-demo-0.0.1-SNAPSHOT.jar
```

### API使用

1. 简单聊天API：

```
GET http://localhost:8080/api/ai/chat?message=你好，请介绍一下Spring AI
```

2. 使用模板的聊天API：

```
GET http://localhost:8080/api/ai/chat-with-template?topic=人工智能
```

## 扩展开发

要添加新的AI功能，可以：

1. 在`AiService`类中添加新的方法
2. 在`AiController`类中添加新的API端点
3. 根据需要修改或创建新的模板

## 参考资料

- [Spring AI 官方文档](https://docs.spring.io/spring-ai/reference/index.html)
- [Spring Boot 官方文档](https://docs.spring.io/spring-boot/docs/current/reference/html/)
- [OpenAI API 文档](https://platform.openai.com/docs/api-reference)